def sum_of_digits(number):
    number = abs(number)
    total = 0
    while number > 0:
        total += number % 10
        number //= 10
    return total

print("=== Sum of Digits Calculator ===")
print("Type 'quit' or 'exit' to stop\n" + "-" * 40)

while True:
    user_input = input("Enter a number: ").strip()
    if user_input.lower() in ['quit', 'exit', 'q']:
        print("Thank you for using Sum of Digits Calculator!")
        break
    try:
        num = int(user_input)
        result = sum_of_digits(num)
        print(f"Sum of digits of {num} is: {result}")

        # Show step by step calculation using loop
        digits = []
        temp = abs(num)
        if temp == 0:
            digits = [0]
        else:
            while temp > 0:
                digits.append(temp % 10)
                temp //= 10
            digits.reverse()

        if len(digits) > 1:
            print("Calculation:", " + ".join(map(str, digits)), "=", result)
    except ValueError:
        print("Please enter a valid number!")
    except KeyboardInterrupt:
        print("\nProgram interrupted. Goodbye!")
        break
