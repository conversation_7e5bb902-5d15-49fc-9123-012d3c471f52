# Program to find all leap years between 2000 to 2050

def is_leap_year(year):
   
    if year % 4 == 0:
        if year % 100 == 0:
            if year % 400 == 0:
                return True
            else:
                return False
        else:
            return True
    else:
        return False

print("Leap years between 2000 to 2051:")
print("-" * 35)

leap_years = []
for year in range(2000, 2051):  # 2051 to include 2050
    if is_leap_year(year):
        leap_years.append(year)
        print(year)

print("-" * 35)
print(f"Total leap years found: {len(leap_years)}")
print(f"Leap years list: {leap_years}")