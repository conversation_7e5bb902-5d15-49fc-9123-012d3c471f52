num = int(input("Enter the number: "))
total = 0
original_num = num

# Extract each digit and add to total
while num > 0:
    digit = num % 10    # Get last digit
    total = total + digit    # Add digit to total
    num = num // 10     # Remove last digit

print("Sum of digits is:", total)

# Show the calculation step by step
temp = original_num
digits = []

# Get all digits
while temp > 0:
    digits.append(temp % 10)
    temp = temp // 10

# Reverse to show in correct order
digits.reverse()

# Show calculation
if len(digits) > 1:
    calculation = ""
    for i in range(len(digits)):
        if i == 0:
            calculation = str(digits[i])
        else:
            calculation = calculation + " + " + str(digits[i])
    print(f"Calculation: {calculation} = {total}")
else:
    print(f"Single digit: {total}")