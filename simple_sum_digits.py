# Simple Sum of Digits Calculator for Beginners

print("Sum of Digits Calculator")
print("=" * 25)

# Get number from user
number = int(input("Enter a number: "))

# Calculate sum of digits
sum_total = 0
original_number = number

# Handle negative numbers
if number < 0:
    number = -number  # Make it positive

# Extract each digit and add to sum
while number > 0:
    digit = number % 10    # Get the last digit
    sum_total = sum_total + digit    # Add digit to sum
    number = number // 10   # Remove the last digit

# Show the result
print(f"Sum of digits of {original_number} is: {sum_total}")