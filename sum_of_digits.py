# Program to find sum of digits of a given number

def sum_of_digits(number):
    """
    Function to calculate sum of digits of a number
    """
    total = 0
    number = abs(number)  # Handle negative numbers
    
    while number > 0:
        digit = number % 10  # Get last digit
        total += digit       # Add digit to total
        number = number // 10  # Remove last digit
    
    return total

# Method 1: Using while loop
print("=== Sum of Digits Calculator ===")
num = int(input("Enter a number: "))

if num == 0:
    print("Sum of digits of 0 is: 0")
else:
    result = sum_of_digits(num)
    print(f"Sum of digits of {num} is: {result}")

print("\n" + "="*40)

# Method 2: Using string conversion (alternative approach)
print("Alternative method using string conversion:")
num2 = input("Enter another number: ")

try:
    # Convert to string and sum each digit
    digit_sum = sum(int(digit) for digit in num2 if digit.isdigit())
    print(f"Sum of digits of {num2} is: {digit_sum}")
except ValueError:
    print("Please enter a valid number")

print("\n" + "="*40)

# Method 3: Recursive approach
def sum_digits_recursive(n):
    """
    Recursive function to find sum of digits
    """
    n = abs(n)
    if n == 0:
        return 0
    else:
        return (n % 10) + sum_digits_recursive(n // 10)

print("Recursive method:")
num3 = int(input("Enter a number for recursive calculation: "))
recursive_result = sum_digits_recursive(num3)
print(f"Sum of digits of {num3} using recursion is: {recursive_result}")
